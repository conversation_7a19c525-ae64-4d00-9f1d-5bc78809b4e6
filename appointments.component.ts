import {
  Component,
  ElementRef,
  <PERSON>L<PERSON>ener,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AppointmentService } from '../../../shared/services/appointment.service';
import {
  Appointment,
  getInProgressAppointments,
  getNotInProgressAppointments,
} from '../../../shared/models/appointment.model';
import {Subject, Subscription} from 'rxjs';
import * as moment from 'moment';
import { MatDialog } from '@angular/material/dialog';
import { AppoitmentDialogComponent } from '../../../shared/components/appoitment-dialog/appoitment-dialog.component';
import {
  APPOINTMENT_STATES_OBJECT, CALLS_TYPES,
  NOTIFICATION_TIMEOUT,
  PROFILE_TYPES,
  TIMEOFF_TYPES_OBJECT,
} from '../../../shared/constants/defaults.consts';
import { SocketService } from 'src/app/core/services/socket.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { ErrorService } from 'src/app/shared/services/error.service';
import { Timeoff, transformTypes } from 'src/app/shared/models/timeoff.model';
import { Profile } from 'src/app/shared/models/profile.model';
import { StorageService } from 'src/app/core/services/storage.service';
import { ProfileService } from 'src/app/shared/services/profile.service';
import { borderTopRightRadius } from 'html2canvas/dist/types/css/property-descriptors/border-radius';
import { TranslateService } from '@ngx-translate/core';
import {debounceTime} from 'rxjs/operators';

@Component({
  selector: 'app-appointments',
  templateUrl: './appointments.component.html',
  styleUrls: ['./appointments.component.scss'],
})
export class AppointmentsComponent implements OnInit, OnDestroy {
  @ViewChild('timeoffsOptionsRef') timeoffsOptionsRef: ElementRef;

  public date: moment.Moment = moment();
  public isLoadingAppointments = false;
  public appointments: Appointment[] = [];
  public inProgressAppointments: Appointment[] = [];
  public page: number = 1;
  public limit: number = 10;
  public pages: number;
  public searchText: string = '';
  public treated: boolean;
  public toggleTreated: boolean;
  public justUpdatedAppointmentIds: string[] = [];
  public states: string[];
  public dailyBreak: Timeoff = {};
  public timeoffs: Timeoff[] = [];
  public suggestion: string = '08:00';
  public showTimeoffs: boolean = false;
  public allowedDoctors: Profile[] = [];

  public doctors: string[] = [];

  private getAppointmentsSubscription: Subscription;
  private getTimeoffsSubscription: Subscription;
  public isArabicLanguageActive: boolean;

  public isReceptionist: boolean = false;

  private searchInputChanged: Subject<any> = new Subject<any>();
  private searchInputSubscription: Subscription;
  debounceTime = 300;
  selectedViewType: 'DAILY' | 'WEEKLY' | 'MONTHLY' = 'DAILY';

  @HostListener('document:click', ['$event'])
  clickout(event: any) {
    if (!this.timeoffsOptionsRef.nativeElement.contains(event.target)) {
      this.showTimeoffs = false;
    }
  }
  constructor(
    private appointmentService: AppointmentService,
    private dialog: MatDialog,
    private socketService: SocketService,
    private storageService: StorageService,
    private errorService: ErrorService,
    private translate: TranslateService
  ) {
    this.isArabicLanguageActive = storageService.getCurrentLanguage() === 'ar';
    translate.onLangChange.subscribe(() => {
      this.isArabicLanguageActive = translate.currentLang === 'ar';
    });
  }

  ngOnInit(): void {
    this.getDoctors();
    this.getTimeoffs();
    this.setInitValues();
    this.getAppointments();
    this.getSuggestion();
    this.initsocketListners();


    this.searchInputSubscription = this.searchInputChanged
      .pipe(
        debounceTime(this.debounceTime),
      )
      .subscribe(($event) => {
        this.searchText = $event.target?.value;
        this.resetData();
        this.getAppointments();
      });
  }
  getDoctors() {
    this.allowedDoctors = this.storageService.getDoctors();

    // Check if current user is a receptionist
    const currentUser = ""; /*this.storageService.getCurrentUser();*/
    this.isReceptionist = true ;/*currentUser?.profile?.title === 'RECEPTIONIST';*/

    if (this.isReceptionist) {
      // For receptionists: Show ALL doctors by default (no filter applied)
      this.doctors = []; // Empty array means no doctor filter - show all doctors
    } else {
      // For other roles: Use default doctors (existing behavior)
      this.doctors = this.storageService
        .getDoctorsByDefault()
        .map((x) => x._id || '')
        .filter((x) => x !== '');
    }
  }

  getSuggestion(): void {
    const date = moment(this.date)
      .utcOffset(0)
      .set({ hour: 0, minute: 0 })
      .format('YYYY-MM-DD');
    this.appointmentService.timeProposition(date).subscribe((res) => {
      if (res) {
        const suggestions = res.suggestions.map((x: any) => {
          return {
            inWorkHours: x.inWorkHours,
            time: moment(x.time).format('HH:mm'),
          };
        });
        this.suggestion = suggestions[0].time;
      }
    });
  }

  setInitValues() {
    this.defineTreated();
    this.handleDailyBreak();
    this.handleTimeoffs();
  }

  defineTreated(fromMonthView: boolean = false) {
    if (!fromMonthView) {
      if (!moment(new Date()).isAfter(moment(this.date), 'day')) {
        this.toggleTreated = false;
        this.treated = false;
      } else {
        this.toggleTreated = true;
        this.treated = true;
      }
    }
  }

  toggleTreatedChange($event: any) {
    this.treated = $event.target.checked;
    this.defineStates();
    this.searchText = '';
    this.resetData();
    this.getAppointments();
  }

  defineStates() {
    if (!this.treated) {
      this.states = [
        APPOINTMENT_STATES_OBJECT.approved,
        APPOINTMENT_STATES_OBJECT.inProgress,
        APPOINTMENT_STATES_OBJECT.canceled,
        APPOINTMENT_STATES_OBJECT.almostCompleted,
      ];
    } else {
      this.states = [APPOINTMENT_STATES_OBJECT.completed];
    }
  }

  getAppointments() {
    this.defineStates();
    this.isLoadingAppointments = true;

    let interval: any = {};
    const isTypeDaily = this.selectedViewType === 'DAILY';
    if (this.selectedViewType === 'WEEKLY') {
      interval = this.getWeekInterval(this.date);
    } else if (this.selectedViewType === 'MONTHLY') {
      interval = this.getMonthInterval(this.date);
    }
    this.getAppointmentsSubscription = this.appointmentService
      .getAppointments(
        this.states,
        this.date.format('YYYY-MM-DD'),
        this.searchText,
        isTypeDaily ? this.page : undefined,
        isTypeDaily ? this.limit : undefined,
        this.doctors ? !this.isReceptionist : null,
        interval
      )
      .subscribe((res) => {
        this.isLoadingAppointments = false;
        this.page = this.page + 1;
        if (!this.pages || this.pages !== Math.ceil(res.total / this.limit)) {
          this.setPages(res.total);
        }
        this.appointments.filter(
          (x: any) => !res.docs.some((w: any) => x._id === w._id)
        );
        this.inProgressAppointments.filter(
          (x: any) => !res.docs.some((w: any) => x._id === w._id)
        );
        this.pushAppointments(res.docs as Appointment[]);
      });
  }

  getWeekInterval(date: moment.Moment) {
    const start = date.clone().startOf('week');
    const end = date.clone().endOf('week');
    return { startDate: start, endDate: end};
  }
  getMonthInterval(date: moment.Moment) {
    const start = date.clone().startOf('month');
    const end = date.clone().endOf('month');
    return { startDate: start, endDate: end};
  }

  getTimeoffs() {
    this.timeoffs = [];
    this.appointmentService
      .getTimeOffs(this.date.format('YYYY-MM-DD'), this.doctors)
      .subscribe((res: any) => {
        if (res.dailyBreak && res.dailyBreak.isActive) {
          this.dailyBreak = res.dailyBreak;
        }
        if (res.timeoffs) {
          this.timeoffs = this.timeoffs.concat(res.timeoffs);
        }
      });
  }

  handleDailyBreak() {
    setInterval(() => {
      if (!this.treated) {
        if (this.selectedViewType !== 'DAILY') return;
        if (
          this.dailyBreak.startTime &&
          new Date(this.dailyBreak.startTime).getTime() >
            new Date().getTime() &&
          this.dailyBreak.isActive
        ) {
          if (
            !this.appointments.some((a: any) => a._id === this.dailyBreak._id)
          ) {
            /*this.appointments.push(this.dailyBreak);
            this.appointments = JSON.parse(
              JSON.stringify(this.sortedAppointments(this.appointments, false))
            );*/
          }
        } else {
          if (
            this.dailyBreak.startTime &&
            this.dailyBreak.endTime &&
            new Date(this.dailyBreak.startTime).getTime() <=
              new Date().getTime() &&
            new Date(this.dailyBreak.endTime).getTime() >=
              new Date().getTime() &&
            this.dailyBreak.isActive
          ) {
            this.appointments = this.appointments.filter(
              (x: any) => x?._id !== this.dailyBreak._id
            );
            if (
              !this.inProgressAppointments.some(
                (a: any) => a._id === this.dailyBreak._id
              )
            ) {
              this.inProgressAppointments.push(this.dailyBreak);
              this.inProgressAppointments = JSON.parse(
                JSON.stringify(
                  this.sortedAppointments(this.inProgressAppointments, false)
                )
              );
            }
          } else {
            this.inProgressAppointments = this.inProgressAppointments.filter(
              (x: any) => x?._id !== this.dailyBreak._id
            );
          }
        }
      }
    }, 1000);
  }
  handleTimeoffs() {
    setInterval(() => {
      if (!this.treated) {
        if (this.selectedViewType !== 'DAILY') return;

        this.timeoffs.map((timeoff: Timeoff) => {
          if (
            timeoff.startTime &&
            timeoff.endTime &&
            new Date(timeoff.startTime).getTime() <= new Date().getTime() &&
            new Date(timeoff.endTime).getTime() >= new Date().getTime() &&
            timeoff.isActive
          ) {
            this.appointments = this.appointments.filter(
              (x: any) => x?._id !== timeoff._id
            );
            if (
              !this.inProgressAppointments.some(
                (a: any) => a._id === timeoff._id
              )
            ) {
              this.inProgressAppointments.push(timeoff);

              this.inProgressAppointments = JSON.parse(
                JSON.stringify(
                  this.sortedAppointments(this.inProgressAppointments, false)
                )
              );
            }
          } else {
            this.inProgressAppointments = this.inProgressAppointments.filter(
              (x: any) => x?._id !== timeoff._id
            );
          }
        });
      }
    }, 1000);
  }
  isAppointment(appointment: Appointment | Timeoff): boolean {
    return 'patient' in appointment;
  }

  pushAppointments(appointments: Appointment[]) {
    this.appointments = this.appointments.concat(...getNotInProgressAppointments(appointments));
    this.inProgressAppointments = this.inProgressAppointments.concat(
      ...getInProgressAppointments(appointments)
    );
  }

  setPages(total: number) {
    this.pages = Math.ceil(total / this.limit);
  }

  ngOnDestroy(): void {
    this.socketService.removeAllListeners('appointment-create');
    this.socketService.removeAllListeners('timeoff-create');
    this.socketService.removeAllListeners('timeoff-delete');
    if (this.getAppointmentsSubscription) {
      this.getAppointmentsSubscription.unsubscribe();
    }
    if (this.getTimeoffsSubscription) {
      this.getTimeoffsSubscription.unsubscribe();
    }
  }

  onScroll() {
    if(this.selectedViewType !== 'DAILY') return;
    if (this.page <= this.pages) {
      this.getAppointments();
    }
  }

  chosenDayHandler(data: any) {
    this.date = moment(data.value).clone();
    this.dateChange();
  }

  onDayChange(directionType: string) {
    let type: 'day'  | 'week' | 'month' = 'day';
    if (this.selectedViewType === 'DAILY') {
      type = 'day';
    } else if (this.selectedViewType === 'WEEKLY') {
      type = 'week';
    }
    else if (this.selectedViewType === 'MONTHLY') {
      type = 'month';
    }
    if (directionType === 'next') {
      this.date = this.date.clone().add(1, type);
    } else {
      this.date = this.date.clone().subtract(1, type);
    }
    this.getSuggestion();
    this.dateChange();
  }

  resetData() {
    this.page = 1;
    this.appointments = [];
    this.inProgressAppointments = [];
  }

  dateChange(fromMonthView: boolean = false) {
    this.searchText = '';
    this.resetData();
    this.defineTreated(fromMonthView);
    this.getAppointments();
    this.getTimeoffs();
  }

  searchAppointments($event: any) {
    this.searchInputChanged.next($event);
  }

  createClick() {
    const dialogRef = this.dialog.open(AppoitmentDialogComponent, {
      width: '600px',
      data: {
        type: 'CREATE',
        initTime: moment().format('HH:mm'), // TODO: Find better time display
        appointment: {
          date: this.date
            .utcOffset(0)
            .set({ hour: 0, minute: 0, second: 0 })
            .toDate(),
        },
      },
    });

    dialogRef.afterClosed().subscribe((appointment) => {
      if (appointment && appointment._id) {
        this.manageCreate(appointment, false);
      }
    });
  }

  manageUpdate(
    appointment: Appointment,
    inProgress: boolean = false,
    scroll: boolean = true
  ) {
    if (this.isWorthTheChange(appointment)) {
      this.setAppointment(appointment, inProgress);
      this.manageAppointmentsState();
      setTimeout(() => {
        if (
          [...this.appointments, ...this.inProgressAppointments].some(
            (app) => app._id === appointment._id
          ) &&
          appointment._id
        ) {
          const itemToScrollTo = document.getElementById(appointment._id);
          this.setLatestUpdate(appointment);
          if (itemToScrollTo && scroll) {
            itemToScrollTo.scrollIntoView({ behavior: 'smooth' });
          }
        }
      }, 0);
    } else {
      if (!inProgress) {
        this.appointments = this.appointments.filter(
          (x: Appointment) => x._id !== appointment._id
        );
      } else {
        this.inProgressAppointments = this.inProgressAppointments.filter(
          (x: Appointment) => x._id !== appointment._id
        );
      }
    }
    this.getSuggestion();
  }

  manageCreate(appointment: Appointment, scroll: boolean = true) {
    if (this.isWorthTheChange(appointment)) {
      if(this.doctors.includes((appointment.doctor as Profile)?._id as string)) {
        this.appointments.push(appointment);
        this.manageAppointmentsState();
        this.sortAppointments();
        setTimeout(() => {
          if (appointment._id) {
            const itemToScrollTo = document.getElementById(appointment._id);
            if (itemToScrollTo && scroll) {
              itemToScrollTo.scrollIntoView({ behavior: 'smooth' });
              this.setLatestUpdate(appointment);
            }
          }
        }, 0);
      }
    }
    this.getSuggestion();
  }

  setLatestUpdate(appointment: Appointment) {
    this.justUpdatedAppointmentIds.push(appointment._id as string);
    setTimeout(() => {
      this.justUpdatedAppointmentIds.splice(
        this.justUpdatedAppointmentIds.indexOf(appointment._id as string),
        1
      );
    }, 3000);
  }

  setAppointment(appointment: Appointment, inProgress: boolean) {
    let appointmentIndex;
    const currentAppointmentsGroup = inProgress
      ? this.inProgressAppointments
      : this.appointments;
    const otherAppointmentsGroup = inProgress
      ? this.appointments
      : this.inProgressAppointments;
    appointmentIndex = currentAppointmentsGroup.findIndex(
      (app) =>
        app._id ===
        (!(appointment as any).appointment
          ? appointment._id
          : (appointment as any).appointment._id)
    );
    const oldAppointment = currentAppointmentsGroup[appointmentIndex];

    if (appointment && (appointment as any).appointment) {
      currentAppointmentsGroup.splice(appointmentIndex, 1);
    } else if (
      oldAppointment?.state !== appointment?.state &&
      appointment?.state !== APPOINTMENT_STATES_OBJECT.almostCompleted &&
      ([oldAppointment?.state, appointment.state].includes(
        APPOINTMENT_STATES_OBJECT.inProgress
      ) ||
        [oldAppointment?.state, appointment.state].includes(
          APPOINTMENT_STATES_OBJECT.almostCompleted
        ))
    ) {
      this.pushAppointmentInGroup(appointment, otherAppointmentsGroup);
      currentAppointmentsGroup.splice(appointmentIndex, 1);
    } else {
      this.pushAppointmentInGroup(appointment, currentAppointmentsGroup);
    }
  }

  pushAppointmentInGroup(appointment: Appointment, group: Appointment[]) {
    const index = group.findIndex(
      (app) =>
        (new Date(app.startTime as Date).getTime() as number) >=
        (new Date(appointment.startTime as Date).getTime() as number)
    );
    const previousIndex = group.findIndex((app) => appointment._id === app._id);
    if (previousIndex >= 0) {
      group.splice(previousIndex, 1);
    }
    if (index < 0) {
      group.push(appointment);
    } else {
      group.splice(index, 0, appointment);
    }
  }

  sortAppointments() {
    this.appointments = JSON.parse(
      JSON.stringify(this.sortedAppointments(this.appointments, false))
    );
    this.inProgressAppointments = JSON.parse(
      JSON.stringify(this.sortedAppointments(this.inProgressAppointments, true))
    );
  }

  sortedAppointments(
    appointments: Appointment[],
    inProgress: boolean = false
  ): Appointment[] {
    if (!inProgress) {
      appointments = appointments.sort((appointment1, appointment2) => {
        if (appointment1.startTime && appointment2.startTime) {
          return (
            new Date(appointment1.startTime).getTime() -
            new Date(appointment2.startTime).getTime()
          );
        }
        return 0;
      });
    } else {
      appointments = appointments.sort((appointment1, appointment2) => {
        if (appointment1?.state && appointment2?.state) {
          if (appointment1?.state > appointment2?.state) {
            return 1;
          } else {
            if (appointment1?.state < appointment2?.state) {
              return -1;
            } else {
              if (appointment1.startTime && appointment2.startTime) {
                return (
                  new Date(appointment1.startTime).getTime() -
                  new Date(appointment2.startTime).getTime()
                );
              }
            }
          }
        }
        return 0;
      });
    }
    return appointments;
  }

  manageAppointmentsState() {
    this.appointments = this.getFilteredAppointments(this.appointments);
    this.inProgressAppointments = this.getFilteredAppointments(
      this.inProgressAppointments
    );
  }

  getFilteredAppointments(appointments: Appointment[]) {
    return appointments.filter(
      (appointment) =>
        appointment.state && this.states.includes(appointment?.state)
    );
  }

  isJustUpdated(appointment: Appointment): boolean {
    return this.justUpdatedAppointmentIds.includes(appointment._id as string);
  }

  initsocketListners() {
    this.socketService.listen('appointment-create').subscribe((res: any) => {
      this.manageCreate(res.appointment, false);
    });
    this.socketService.listen('timeoff-create').subscribe((res: any) => {
      if (
        res.timeoff &&
        moment(res.date).format('YYYY-MM-DD') === this.date.format('YYYY-MM-DD')
      ) {
        if (
          !this.dailyBreak ||
          (this.dailyBreak === {} &&
            res.timeoff.type === TIMEOFF_TYPES_OBJECT.dailyBreak)
        ) {
          this.dailyBreak = res.timeoff;
        } else {
          this.timeoffs.push(res.timeoff);
        }
      }
    });
    this.socketService.listen('timeoff-delete').subscribe((res: any) => {
      if (res.timeoff && res.timeoff._id === this.dailyBreak._id) {
        this.dailyBreak = {};
      } else {
        this.timeoffs = this.timeoffs.filter((x) => x._id !== res.timeoff._id);
      }
    });
  }

  isWorthTheChange(appointment: Appointment) {
    return (
      (moment(appointment.date).format('YYYY-MM-DD') ===
        this.date.format('YYYY-MM-DD') &&
        moment(appointment?.startTime).isSameOrBefore(
          moment(this.appointments[this.appointments.length - 1]?.startTime)
        )) ||
      this.appointments.length < 10
    );
  }
  drop(event: CdkDragDrop<Appointment[]>) {
    if (event.previousContainer === event.container) {
      const appointment1 = event.container.data[event.previousIndex];
      const appointment2 = event.container.data[event.currentIndex];
      if (
        appointment1._id &&
        appointment2._id &&
        appointment1._id !== appointment2._id
      ) {
        this.appointmentService
          .switchAppointments(appointment1._id, appointment2._id)
          .subscribe(
            (res: any) => {
              if (res.length === 2) {
                moveItemInArray(
                  event.container.data,
                  event.previousIndex,
                  event.currentIndex
                );
                this.manageUpdate(res[0], false, false);
                this.manageUpdate(res[1], false, false);
              }
            },
            (error: any) => this.errorService.handleError(error)
          );
      }
    } else {
      const appointment = event.previousContainer.data[event.previousIndex];
      appointment.state = APPOINTMENT_STATES_OBJECT.inProgress;
      this.appointmentService.updateAppointment(appointment).subscribe(
        (res: any) => {
          if (res.appointment) {
            transferArrayItem(
              event.previousContainer.data,
              event.container.data,
              event.previousIndex,
              event.currentIndex
            );
            this.manageUpdate(res.appointment, true, false);
          }
        },
        (error: any) => this.errorService.handleError(error)
      );
    }
  }
  manageUpdateTimeoff(event: any) {
    const timeoff = event.timeoff;
    const deleted = event.deleted;
    if (timeoff._id === this.dailyBreak._id) {
      this.dailyBreak = timeoff;
    } else {
      if (deleted) {
        this.timeoffs = this.timeoffs.filter(
          (x: Timeoff) => x._id !== timeoff._id
        );
      } else {
        const index = this.timeoffs.findIndex(
          (x: Timeoff) => x._id === timeoff._id
        );
        this.timeoffs[index] = timeoff;
      }
    }
  }
  selectDoctorChange(doctors: any) {
    this.doctors = doctors;

    // For receptionists: if no doctors selected, show all doctors
    if (this.isReceptionist && (!doctors || doctors.length === 0)) {
      this.doctors = []; // Empty array means show all doctors
    }

    this.dateChange();
  }

  onViewTypeChange(viewType: 'DAILY' | 'WEEKLY' | 'MONTHLY') {
    if (this.selectedViewType !== viewType) {
      this.selectedViewType = viewType;
      this.resetData();
      this.getAppointments();
    }
  }

  updateClick(appointment: Appointment) {
    const dialogRef = this.dialog.open(AppoitmentDialogComponent, {
      width: '600px',
      data: {
        type: CALLS_TYPES.update,
        appointment: JSON.parse(JSON.stringify(appointment)),
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.manageUpdate(result, appointment.state === APPOINTMENT_STATES_OBJECT.inProgress, false);
      }
    });
  }

  frenshVal(viewType: string) {
    switch (viewType) {
      case 'DAILY':
        return 'appointments.daily';
      case 'WEEKLY':
        return 'appointments.weekly';
      case 'MONTHLY':
        return 'appointments.monthly';
        default:
          return '';
    }
  }

  handleMonthDayClick(date: Date) {
    this.selectedViewType = 'DAILY';
    this.date = moment(date);
    this.dateChange(true);
  }

  onWeekPick($event: any) {
    this.date = $event.target.value;
    this.dateChange();
  }

  onMonthPick($event: any) {
    this.date = $event;
    this.dateChange(true);
  }
}
