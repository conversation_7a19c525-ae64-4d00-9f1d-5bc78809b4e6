import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { Direction } from '@angular/cdk/bidi';
import {StorageService} from "../../../../core/services/storage.service";
import {User} from "../../../../shared/models/user.model";
import {Hospital} from "../../../../shared/models/hospital.model";
import {HospitalService} from "../../../../shared/services/hospital.service";

@Component({
  selector: 'app-other-configurations',
  templateUrl: './other-configurations.component.html',
  styleUrls: ['./other-configurations.component.scss']
})
export class OtherConfigurationsComponent implements OnInit {
  @Input() dir: Direction = 'ltr';
  @Output() changeStatus: EventEmitter<boolean> = new EventEmitter<boolean>();

  configObject = {
    prescriptionHeader: false,
  };
  user: User;
  hospital: Hospital;
  constructor(private storageService: StorageService, private hospitalService: HospitalService) {

  }

  ngOnInit(): void {
    this.user = this.storageService.getUser();

    this.hospital = this.user.profile?.hospital as Hospital;

    this.configObject.prescriptionHeader = !!this.hospital?.prescriptionHeader;
  }

  changePrescriptionHeader(checked: boolean) {
    // First emit that there are unsaved changes
    this.changeStatus.emit(false);

    const updatedHospital: Hospital = {_id: this.hospital._id, prescriptionHeader: checked};
    this.hospitalService.editHospital(updatedHospital).subscribe((hospital: Hospital) => {
      if (this.user?.profile?.hospital){
        this.user.profile.hospital.prescriptionHeader = checked;
      }
      // After successful save, emit that changes are saved
      this.changeStatus.emit(true);
    });
  }

  save() {
    // Other configurations are saved automatically when changed
    // This method is called by the parent config component
    this.changeStatus.emit(true);
  }
}
